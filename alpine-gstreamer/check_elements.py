#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst
import sys

def check_gstreamer_elements():
    """Check GStreamer installation and available elements"""
    
    # Initialize GStreamer
    Gst.init(None)
    
    print("=== GStreamer Installation Check ===")
    print()
    
    # Check GStreamer version
    version = Gst.version()
    print(f"GStreamer version: {version.major}.{version.minor}.{version.micro}.{version.nano}")
    print()
    
    # List of important elements for HLS streaming
    elements_to_check = [
        # HLS specific
        "hlsdemux2",
        "hlsdemux", 
        "hlssink",
        "hlssink2",
        
        # HTTP/Network
        "souphttpsrc",
        "curlhttpsrc",
        
        # Demuxing/Decoding
        "uridecodebin",
        "decodebin",
        "parsebin",
        "adaptivedemux",
        
        # High-level
        "playbin",
        "playbin3",
        
        # Video/Audio processing
        "videoconvert",
        "audioresample",
        "audioconvert",
        
        # Sinks
        "autovideosink",
        "autoaudiosink",
        "xvimagesink",
        "alsasink",
        "pulsesink",
        
        # Queues and utilities
        "queue",
        "queue2",
        "multiqueue"
    ]
    
    print("Checking elements:")
    print("-" * 50)
    
    available = []
    missing = []
    
    for element_name in elements_to_check:
        element = Gst.ElementFactory.make(element_name, None)
        if element:
            print(f"✓ {element_name:<20} - AVAILABLE")
            available.append(element_name)
            
            # Get additional info for key elements
            if element_name in ["hlsdemux2", "hlsdemux", "souphttpsrc"]:
                factory = element.get_factory()
                if factory:
                    plugin_name = factory.get_plugin_name()
                    description = factory.get_description()
                    print(f"  └─ Plugin: {plugin_name}")
                    print(f"  └─ Description: {description}")
        else:
            print(f"✗ {element_name:<20} - MISSING")
            missing.append(element_name)
    
    print()
    print("=" * 60)
    print("SUMMARY:")
    print(f"Available elements: {len(available)}/{len(elements_to_check)}")
    print()
    
    # HLS capability assessment
    print("HLS STREAMING CAPABILITY:")
    if "hlsdemux2" in available:
        print("✓ EXCELLENT - hlsdemux2 available (latest HLS support)")
    elif "hlsdemux" in available:
        print("⚠ GOOD - hlsdemux available (older HLS support)")
    elif "uridecodebin" in available:
        print("⚠ BASIC - uridecodebin available (basic HLS support)")
    else:
        print("✗ POOR - No HLS demux elements found")
    
    print()
    print("NETWORK CAPABILITY:")
    if "souphttpsrc" in available:
        print("✓ souphttpsrc available (recommended for HLS)")
    elif "curlhttpsrc" in available:
        print("⚠ curlhttpsrc available (alternative HTTP source)")
    else:
        print("✗ No HTTP source elements found")
    
    print()
    print("RECOMMENDED PIPELINE ELEMENTS:")
    if "hlsdemux2" in available and "souphttpsrc" in available:
        print("✓ Use: souphttpsrc ! hlsdemux2 ! decodebin ! ...")
    elif "hlsdemux" in available and "souphttpsrc" in available:
        print("⚠ Use: souphttpsrc ! hlsdemux ! decodebin ! ...")
    elif "uridecodebin" in available:
        print("⚠ Use: uridecodebin uri=<hls_url> ! ...")
    else:
        print("✗ No suitable HLS pipeline elements available")
    
    # List all available plugins
    print()
    print("=" * 60)
    print("AVAILABLE PLUGINS:")
    registry = Gst.Registry.get()
    plugins = registry.get_plugin_list()
    
    plugin_names = [plugin.get_name() for plugin in plugins]
    plugin_names.sort()
    
    print(f"Total plugins: {len(plugin_names)}")
    print("Plugin list:", ", ".join(plugin_names[:20]) + ("..." if len(plugin_names) > 20 else ""))
    
    return available, missing

if __name__ == "__main__":
    try:
        available, missing = check_gstreamer_elements()
        
        if "hlsdemux2" in available or "hlsdemux" in available:
            print("\n🎉 System ready for HLS streaming!")
            sys.exit(0)
        else:
            print("\n⚠️  Limited HLS support - consider using uridecodebin")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error checking elements: {e}")
        sys.exit(1)
