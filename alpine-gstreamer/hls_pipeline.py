#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib
import sys
import argparse
import signal
import time

class HLSPipeline:
    def __init__(self, hls_url, output_format="autovideosink"):
        """
        Initialize HLS pipeline with hlsdemux2
        
        Args:
            hls_url (str): HLS stream URL
            output_format (str): Output sink (autovideosink, filesink, etc.)
        """
        self.hls_url = hls_url
        self.output_format = output_format
        self.pipeline = None
        self.loop = None
        self.bus = None
        
        # Initialize GStreamer
        Gst.init(None)
        
    def check_elements(self):
        """Check if required elements are available"""
        required_elements = ["hlsdemux2", "souphttpsrc", "queue", "videoconvert", "audioresample"]
        fallback_elements = ["hlsdemux", "uridecodebin"]
        
        available = []
        missing = []
        
        print("=== Checking GStreamer Elements ===")
        
        for element in required_elements + fallback_elements:
            if Gst.ElementFactory.make(element, None):
                available.append(element)
                print(f"✓ {element} - Available")
            else:
                missing.append(element)
                print(f"✗ {element} - Missing")
        
        return available, missing
    
    def create_pipeline_hlsdemux2(self):
        """Create pipeline using hlsdemux2 (preferred method)"""
        pipeline_str = f"""
        souphttpsrc location="{self.hls_url}" ! 
        hlsdemux2 name=demux ! 
        queue ! 
        decodebin ! 
        videoconvert ! 
        {self.output_format}
        
        demux. ! 
        queue ! 
        decodebin ! 
        audioresample ! 
        audioconvert ! 
        autoaudiosink
        """
        
        print(f"Creating pipeline with hlsdemux2:")
        print(pipeline_str.strip())
        
        try:
            pipeline = Gst.parse_launch(pipeline_str)
            return pipeline
        except Exception as e:
            print(f"Failed to create hlsdemux2 pipeline: {e}")
            return None
    
    def create_pipeline_hlsdemux(self):
        """Create pipeline using hlsdemux (fallback method)"""
        pipeline_str = f"""
        souphttpsrc location="{self.hls_url}" ! 
        hlsdemux name=demux ! 
        queue ! 
        decodebin ! 
        videoconvert ! 
        {self.output_format}
        
        demux. ! 
        queue ! 
        decodebin ! 
        audioresample ! 
        audioconvert ! 
        autoaudiosink
        """
        
        print(f"Creating pipeline with hlsdemux (fallback):")
        print(pipeline_str.strip())
        
        try:
            pipeline = Gst.parse_launch(pipeline_str)
            return pipeline
        except Exception as e:
            print(f"Failed to create hlsdemux pipeline: {e}")
            return None
    
    def create_pipeline_uridecodebin(self):
        """Create pipeline using uridecodebin (simple fallback)"""
        pipeline_str = f"""
        uridecodebin uri="{self.hls_url}" name=decode ! 
        videoconvert ! 
        {self.output_format}
        
        decode. ! 
        audioresample ! 
        audioconvert ! 
        autoaudiosink
        """
        
        print(f"Creating pipeline with uridecodebin (simple fallback):")
        print(pipeline_str.strip())
        
        try:
            pipeline = Gst.parse_launch(pipeline_str)
            return pipeline
        except Exception as e:
            print(f"Failed to create uridecodebin pipeline: {e}")
            return None
    
    def create_pipeline(self):
        """Create the best available pipeline"""
        available, missing = self.check_elements()
        
        # Try hlsdemux2 first (preferred)
        if "hlsdemux2" in available and "souphttpsrc" in available:
            print("\n=== Using hlsdemux2 (preferred) ===")
            self.pipeline = self.create_pipeline_hlsdemux2()
            if self.pipeline:
                return True
        
        # Fallback to hlsdemux
        if "hlsdemux" in available and "souphttpsrc" in available:
            print("\n=== Using hlsdemux (fallback) ===")
            self.pipeline = self.create_pipeline_hlsdemux()
            if self.pipeline:
                return True
        
        # Final fallback to uridecodebin
        if "uridecodebin" in available:
            print("\n=== Using uridecodebin (simple fallback) ===")
            self.pipeline = self.create_pipeline_uridecodebin()
            if self.pipeline:
                return True
        
        print("❌ Failed to create any pipeline!")
        return False
    
    def on_message(self, bus, message):
        """Handle bus messages"""
        t = message.type
        
        if t == Gst.MessageType.EOS:
            print("End-of-stream reached")
            self.loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}, Debug: {debug}")
            self.loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}, Debug: {debug}")
        elif t == Gst.MessageType.STATE_CHANGED:
            if message.src == self.pipeline:
                old_state, new_state, pending_state = message.parse_state_changed()
                print(f"Pipeline state changed from {old_state.value_nick} to {new_state.value_nick}")
        elif t == Gst.MessageType.BUFFERING:
            percent = message.parse_buffering()
            print(f"Buffering: {percent}%")
            if percent < 100:
                self.pipeline.set_state(Gst.State.PAUSED)
            else:
                self.pipeline.set_state(Gst.State.PLAYING)
    
    def run(self):
        """Run the pipeline"""
        if not self.create_pipeline():
            return False
        
        # Set up bus
        self.bus = self.pipeline.get_bus()
        self.bus.add_signal_watch()
        self.bus.connect("message", self.on_message)
        
        # Create main loop
        self.loop = GLib.MainLoop()
        
        # Set up signal handlers
        def signal_handler(sig, frame):
            print("\nReceived interrupt signal, stopping pipeline...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start pipeline
        print(f"\n=== Starting HLS stream: {self.hls_url} ===")
        ret = self.pipeline.set_state(Gst.State.PLAYING)
        
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Failed to start pipeline")
            return False
        
        try:
            # Run main loop
            self.loop.run()
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        finally:
            self.stop()
        
        return True
    
    def stop(self):
        """Stop the pipeline"""
        if self.pipeline:
            self.pipeline.set_state(Gst.State.NULL)
        if self.loop and self.loop.is_running():
            self.loop.quit()

def main():
    parser = argparse.ArgumentParser(description="HLS Stream Player using GStreamer")
    parser.add_argument("url", help="HLS stream URL")
    parser.add_argument("--output", "-o", default="autovideosink", 
                       help="Output sink (autovideosink, xvimagesink, filesink, etc.)")
    parser.add_argument("--debug", "-d", action="store_true", 
                       help="Enable debug output")
    
    args = parser.parse_args()
    
    if args.debug:
        import os
        os.environ["GST_DEBUG"] = "3"
    
    # Validate URL
    if not args.url.startswith(("http://", "https://")):
        print("Error: URL must start with http:// or https://")
        return 1
    
    # Create and run pipeline
    player = HLSPipeline(args.url, args.output)
    
    try:
        success = player.run()
        return 0 if success else 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
