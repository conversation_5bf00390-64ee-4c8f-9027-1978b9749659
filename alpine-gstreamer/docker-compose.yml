version: '3.8'

services:
  alpine-gstreamer:
    build: .
    image: alpine-gstreamer:latest
    container_name: hls-streamer
    
    # For X11 display (Linux)
    environment:
      - DISPLAY=${DISPLAY}
      - GST_DEBUG=2
    
    volumes:
      # X11 socket for video output
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      # Output directory for file saves
      - ./output:/app/output
    
    # Network mode for accessing external HLS streams
    network_mode: host
    
    # Keep container running for interactive use
    stdin_open: true
    tty: true
    
    # Override default command for interactive use
    command: /bin/sh
    
    # Restart policy
    restart: unless-stopped

  # Service for running element check
  element-check:
    build: .
    image: alpine-gstreamer:latest
    container_name: gst-element-check
    command: python3 check_elements.py
    profiles:
      - tools

  # Service for streaming a specific URL
  hls-stream:
    build: .
    image: alpine-gstreamer:latest
    container_name: hls-stream
    environment:
      - DISPLAY=${DISPLAY}
      - GST_DEBUG=2
      - HLS_URL=${HLS_URL:-https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8}
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - ./output:/app/output
    network_mode: host
    command: python3 hls_pipeline.py "${HLS_URL:-https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8}"
    profiles:
      - stream
