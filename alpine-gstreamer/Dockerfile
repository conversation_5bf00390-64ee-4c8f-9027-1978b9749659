FROM alpine:latest

# Install build dependencies and GStreamer
RUN apk update && \
    apk add --no-cache \
    # Build tools
    build-base \
    cmake \
    meson \
    ninja \
    pkgconfig \
    git \
    # Core dependencies
    glib-dev \
    gobject-introspection-dev \
    # GStreamer core
    gstreamer \
    gstreamer-dev \
    gst-plugins-base \
    gst-plugins-base-dev \
    gst-plugins-good \
    gst-plugins-bad \
    gst-plugins-ugly \
    gst-libav \
    # Additional dependencies for HLS and networking
    libsoup-dev \
    curl-dev \
    openssl-dev \
    # Python support
    python3 \
    python3-dev \
    py3-pip \
    py3-gobject3 \
    py3-gst \
    # Debugging tools
    gst-plugins-base-tools \
    && rm -rf /var/cache/apk/*

# Install additional GStreamer plugins from edge repository for latest features
RUN echo "http://dl-cdn.alpinelinux.org/alpine/edge/main" >> /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/edge/community" >> /etc/apk/repositories && \
    echo "http://dl-cdn.alpinelinux.org/alpine/edge/testing" >> /etc/apk/repositories && \
    apk update && \
    apk add --no-cache \
    gstreamer \
    gst-plugins-base \
    gst-plugins-good \
    gst-plugins-bad \
    gst-plugins-ugly \
    gst-libav \
    && rm -rf /var/cache/apk/*

# Build latest GStreamer from source to ensure hlsdemux2 is available
WORKDIR /tmp/gstreamer-build

# Clone and build GStreamer core (latest stable)
RUN git clone --depth 1 --branch 1.24.8 https://gitlab.freedesktop.org/gstreamer/gstreamer.git && \
    cd gstreamer && \
    meson setup build \
        --prefix=/usr \
        --libdir=lib \
        --buildtype=release \
        -Dgst-plugins-bad:hls=enabled \
        -Dgst-plugins-bad:adaptivedemux2=enabled \
        -Dgst-plugins-good:soup=enabled \
        -Dintrospection=enabled \
        -Dpython=enabled && \
    meson compile -C build && \
    meson install -C build && \
    cd .. && \
    rm -rf gstreamer

# Verify GStreamer installation and HLS support
RUN gst-inspect-1.0 --version && \
    echo "=== Checking for HLS elements ===" && \
    gst-inspect-1.0 | grep -E "(hls|adaptive)" || echo "No HLS elements found in grep" && \
    echo "=== Checking hlsdemux2 specifically ===" && \
    gst-inspect-1.0 hlsdemux2 || echo "hlsdemux2 not found" && \
    echo "=== Checking hlsdemux ===" && \
    gst-inspect-1.0 hlsdemux || echo "hlsdemux not found" && \
    echo "=== Checking souphttpsrc ===" && \
    gst-inspect-1.0 souphttpsrc || echo "souphttpsrc not found" && \
    echo "=== Available plugins ===" && \
    gst-inspect-1.0 | head -20

# Clean up build dependencies to reduce image size
RUN apk del build-base cmake meson ninja git && \
    rm -rf /tmp/gstreamer-build /var/cache/apk/*

# Set up working directory
WORKDIR /app

# Copy application files
COPY . /app/

# Set environment variables
ENV GST_DEBUG=2
ENV GST_PLUGIN_PATH=/usr/lib/gstreamer-1.0
ENV LD_LIBRARY_PATH=/usr/lib:$LD_LIBRARY_PATH

# Default command
CMD ["python3", "hls_pipeline.py"]
