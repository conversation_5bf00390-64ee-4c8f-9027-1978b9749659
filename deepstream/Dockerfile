FROM nvcr.io/nvidia/deepstream:7.0-samples-multiarch AS deepstream-base

# Environment variables
ENV NVIDIA_DRIVER_CAPABILITIES=$NVIDIA_DRIVER_CAPABILITIES,video
ENV LOGLEVEL="INFO"
ENV TZ="Asia/Singapore"

# Pre-cache dependencies
RUN apt update && \
    apt install -y wget curl python3-gi python3-dev python3-gst-1.0 \
    gstreamer1.0-plugins-good gstreamer1.0-plugins-bad gstreamer1.0-plugins-ugly gstreamer1.0-rtsp \
    gstreamer1.0-plugins-base gstreamer1.0-libav \
    libcairo2-dev build-essential graphviz \
    && apt-get -y install cuda-toolkit-12-2 \
    && apt clean

# Try to install newer GStreamer plugins that might include hlsdemux2
RUN apt update && \
    apt install -y software-properties-common && \
    add-apt-repository ppa:gstreamer-developers/ppa -y || echo "PPA not available" && \
    apt update && \
    apt install -y gstreamer1.0-plugins-good gstreamer1.0-plugins-bad --reinstall || echo "Reinstall failed" && \
    apt clean

# Check GStreamer version and available plugins
RUN gst-inspect-1.0 --version && \
    echo "Checking for HLS plugins:" && \
    gst-inspect-1.0 | grep -i hls || echo "No HLS plugins found in basic search" && \
    echo "Checking specifically for hlsdemux2:" && \
    gst-inspect-1.0 hlsdemux2 || echo "hlsdemux2 not found" && \
    echo "Checking for hlsdemux:" && \
    gst-inspect-1.0 hlsdemux || echo "hlsdemux not found"

# Install additional DeepStream components
RUN /opt/nvidia/deepstream/deepstream/user_additional_install.sh

# Install Python packages
RUN pip3 install cuda-python \
    opencv_python==********* requests==2.32.3 redis==5.0.8 jsons==1.6.3

# Install Pyds
RUN curl -O -L https://github.com/NVIDIA-AI-IOT/deepstream_python_apps/releases/download/v1.1.11/pyds-1.1.11-py3-none-linux_x86_64.whl && \
    pip3 install ./pyds-1.1.11-py3-none-linux_x86_64.whl

# Set CUDA alternatives
RUN update-alternatives --set cuda /usr/local/cuda-12.2
# Clean up
RUN rm ./pyds-1.1.11-py3-none-linux_x86_64.whl
RUN apt-get remove -y curl wget 

# Set the working directory
COPY ./ /app
WORKDIR /app

CMD ["python3", "main.py"]