#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst
import sys

def check_hls_elements():
    """Check what HLS-related elements are available"""
    
    # Initialize GStreamer
    Gst.init(None)
    
    print("=== GStreamer HLS Element Availability Check ===")
    print()
    
    # Check GStreamer version
    version = Gst.version()
    print(f"GStreamer version: {version.major}.{version.minor}.{version.micro}.{version.nano}")
    print()
    
    # List of HLS-related elements to check
    hls_elements = [
        "hlsdemux",
        "hlsdemux2", 
        "hlssink",
        "hlssink2",
        "souphttpsrc",
        "uridecodebin",
        "playbin",
        "adaptivedemux"
    ]
    
    print("Checking HLS-related elements:")
    print("-" * 40)
    
    available_elements = []
    
    for element_name in hls_elements:
        element = Gst.ElementFactory.make(element_name, None)
        if element:
            print(f"✓ {element_name} - AVAILABLE")
            available_elements.append(element_name)
            
            # Get element info
            factory = element.get_factory()
            if factory:
                print(f"  Plugin: {factory.get_plugin_name()}")
                print(f"  Description: {factory.get_description()}")
        else:
            print(f"✗ {element_name} - NOT AVAILABLE")
        print()
    
    print("=" * 50)
    print("SUMMARY:")
    print(f"Available HLS elements: {', '.join(available_elements)}")
    
    # Recommendations
    print()
    print("RECOMMENDATIONS:")
    if "hlsdemux2" in available_elements:
        print("✓ Use hlsdemux2 for best HLS support")
    elif "hlsdemux" in available_elements:
        print("⚠ Use hlsdemux (older version, may have limitations)")
    else:
        print("⚠ No HLS demux elements found - consider using uridecodebin or playbin")
    
    if "uridecodebin" in available_elements:
        print("✓ uridecodebin available as fallback option")
    
    if "playbin" in available_elements:
        print("✓ playbin available as high-level fallback option")
    
    return available_elements

if __name__ == "__main__":
    try:
        available = check_hls_elements()
        sys.exit(0)
    except Exception as e:
        print(f"Error checking elements: {e}")
        sys.exit(1)
