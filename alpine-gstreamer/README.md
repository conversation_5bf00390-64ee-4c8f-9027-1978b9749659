# Alpine GStreamer HLS Pipeline

This directory contains a Docker setup for streaming HLS content using GStreamer on Alpine Linux with the latest plugins, including `hlsdemux2`.

## Files

- `Dockerfile` - Alpine-based container with latest GStreamer and HLS support
- `hls_pipeline.py` - Python script for HLS streaming using hlsdemux2
- `check_elements.py` - Utility to check available GStreamer elements
- `README.md` - This file

## Features

- **Alpine Linux base** - Lightweight container
- **Latest GStreamer** - Built from source (v1.24.8) with HLS support
- **hlsdemux2 support** - Latest HLS demuxer with adaptive streaming
- **Fallback support** - Graceful fallback to hlsdemux or uridecodebin
- **Python bindings** - Full GObject introspection support

## Building the Container

```bash
cd alpine-gstreamer
docker build -t alpine-gstreamer .
```

## Usage

### Basic HLS Streaming

Stream an HLS URL to the default video output:

```bash
docker run --rm -it \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix \
  alpine-gstreamer \
  python3 hls_pipeline.py "https://example.com/stream.m3u8"
```

### Check Available Elements

Verify GStreamer installation and available elements:

```bash
docker run --rm alpine-gstreamer python3 check_elements.py
```

### Advanced Usage

#### Custom Output Sink

```bash
# Save to file
docker run --rm -it \
  -v $(pwd)/output:/app/output \
  alpine-gstreamer \
  python3 hls_pipeline.py "https://example.com/stream.m3u8" \
  --output "filesink location=output/stream.mp4"

# Use specific video sink
docker run --rm -it \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix \
  alpine-gstreamer \
  python3 hls_pipeline.py "https://example.com/stream.m3u8" \
  --output "xvimagesink"
```

#### Debug Mode

Enable GStreamer debug output:

```bash
docker run --rm -it \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix \
  alpine-gstreamer \
  python3 hls_pipeline.py "https://example.com/stream.m3u8" --debug
```

## Pipeline Architecture

The HLS pipeline uses the following element hierarchy (in order of preference):

### 1. hlsdemux2 Pipeline (Preferred)
```
souphttpsrc → hlsdemux2 → queue → decodebin → videoconvert → output
                    ↓
                  queue → decodebin → audioresample → audioconvert → autoaudiosink
```

### 2. hlsdemux Pipeline (Fallback)
```
souphttpsrc → hlsdemux → queue → decodebin → videoconvert → output
                   ↓
                 queue → decodebin → audioresample → audioconvert → autoaudiosink
```

### 3. uridecodebin Pipeline (Simple Fallback)
```
uridecodebin → videoconvert → output
      ↓
audioresample → audioconvert → autoaudiosink
```

## Key Features of hlsdemux2

- **Adaptive bitrate streaming** - Automatically switches quality based on bandwidth
- **Better error handling** - More robust against network issues
- **Improved buffering** - Better playback experience
- **Modern HLS features** - Support for latest HLS specifications

## Environment Variables

- `GST_DEBUG` - Set debug level (0-9, default: 2)
- `GST_PLUGIN_PATH` - Additional plugin search paths
- `DISPLAY` - X11 display for video output

## Troubleshooting

### No Video Output
- Ensure X11 forwarding is set up correctly
- Try different output sinks: `autovideosink`, `xvimagesink`, `ximagesink`

### Network Issues
- Check if the HLS URL is accessible
- Verify firewall settings
- Try with `--debug` flag for detailed logs

### Missing Elements
Run the element checker:
```bash
docker run --rm alpine-gstreamer python3 check_elements.py
```

### Build Issues
If the build fails, try:
- Check internet connectivity
- Verify Alpine package repositories are accessible
- Consider using a different GStreamer version in the Dockerfile

## Example HLS URLs for Testing

```bash
# Big Buck Bunny (test stream)
python3 hls_pipeline.py "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8"

# Apple's test stream
python3 hls_pipeline.py "https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_fmp4/master.m3u8"
```

## Performance Notes

- The container builds GStreamer from source, which takes time but ensures latest features
- For production use, consider using pre-built packages if hlsdemux2 is available
- Alpine Linux provides a smaller footprint compared to Ubuntu/Debian based images
