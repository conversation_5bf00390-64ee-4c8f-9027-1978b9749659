#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib
import sys
import signal

class AdaptiveHLSPipeline:
    def __init__(self):
        # Initialize GStreamer
        Gst.init(None)
        
        # Determine best pipeline strategy
        self.pipeline_type = self.determine_pipeline_type()
        print(f"Using pipeline type: {self.pipeline_type}")
        
        # Create pipeline
        self.pipeline = Gst.Pipeline.new("adaptive-hls-pipeline")
        
        # Create elements based on strategy
        self.create_elements()
        
        # Add elements to pipeline
        self.add_elements_to_pipeline()
        
        # Link elements
        self.link_elements()
        
        # Connect signals
        self.connect_signals()
        
        # Create main loop
        self.loop = GLib.MainLoop()
    
    def determine_pipeline_type(self):
        """Determine the best pipeline type based on available elements"""
        
        # Check for hlsdemux2 (best option)
        if Gst.ElementFactory.make("hlsdemux2", None):
            return "hlsdemux2"
        
        # Check for hlsdemux (fallback)
        elif Gst.ElementFactory.make("hlsdemux", None):
            return "hlsdemux"
        
        # Check for uridecodebin (high-level fallback)
        elif Gst.ElementFactory.make("uridecodebin", None):
            return "uridecodebin"
        
        # Check for playbin (ultimate fallback)
        elif Gst.ElementFactory.make("playbin", None):
            return "playbin"
        
        else:
            raise RuntimeError("No suitable HLS pipeline elements found!")
    
    def create_elements(self):
        """Create elements based on pipeline type"""
        
        if self.pipeline_type in ["hlsdemux2", "hlsdemux"]:
            self.create_manual_pipeline()
        elif self.pipeline_type == "uridecodebin":
            self.create_uridecodebin_pipeline()
        elif self.pipeline_type == "playbin":
            self.create_playbin_pipeline()
    
    def create_manual_pipeline(self):
        """Create manual pipeline with hlsdemux/hlsdemux2"""
        
        # Source
        self.souphttpsrc = Gst.ElementFactory.make("souphttpsrc", "source")
        
        # HLS demuxer
        self.hlsdemux = Gst.ElementFactory.make(self.pipeline_type, "demux")
        
        # Queues
        self.queue1 = Gst.ElementFactory.make("queue", "queue1")
        self.queue2 = Gst.ElementFactory.make("queue", "queue2")
        
        # Transport stream demuxer
        self.tsdemux = Gst.ElementFactory.make("tsdemux", "tsdemux")
        
        # H264 parser
        self.h264parse = Gst.ElementFactory.make("h264parse", "h264parse")
        
        # NVIDIA decoder
        self.nvv4l2decoder = Gst.ElementFactory.make("nvv4l2decoder", "decoder")
        
        # Video converter
        self.nvvideoconvert = Gst.ElementFactory.make("nvvideoconvert", "converter")
        
        # Sink
        self.nveglglessink = Gst.ElementFactory.make("nveglglessink", "sink")
        
        # Store elements for checking
        self.elements = [
            ("souphttpsrc", self.souphttpsrc),
            (self.pipeline_type, self.hlsdemux),
            ("queue1", self.queue1),
            ("queue2", self.queue2),
            ("tsdemux", self.tsdemux),
            ("h264parse", self.h264parse),
            ("nvv4l2decoder", self.nvv4l2decoder),
            ("nvvideoconvert", self.nvvideoconvert),
            ("nveglglessink", self.nveglglessink)
        ]
    
    def create_uridecodebin_pipeline(self):
        """Create uridecodebin-based pipeline"""
        
        # URI decode bin
        self.uridecodebin = Gst.ElementFactory.make("uridecodebin", "source")
        
        # Queue
        self.queue = Gst.ElementFactory.make("queue", "queue")
        
        # Video converter
        self.nvvideoconvert = Gst.ElementFactory.make("nvvideoconvert", "converter")
        
        # Sink
        self.nveglglessink = Gst.ElementFactory.make("nveglglessink", "sink")
        
        # Store elements for checking
        self.elements = [
            ("uridecodebin", self.uridecodebin),
            ("queue", self.queue),
            ("nvvideoconvert", self.nvvideoconvert),
            ("nveglglessink", self.nveglglessink)
        ]
    
    def create_playbin_pipeline(self):
        """Create playbin-based pipeline"""
        
        # Playbin
        self.playbin = Gst.ElementFactory.make("playbin", "player")
        
        # Store elements for checking
        self.elements = [
            ("playbin", self.playbin)
        ]
    
    def set_element_properties(self):
        """Set properties based on pipeline type"""
        
        hls_url = "https://admin-srs.volks-dev.knizsoft.com/live/3c0c5818-03b3-4959-baae-8b8817d865ba.m3u8"
        
        if self.pipeline_type in ["hlsdemux2", "hlsdemux"]:
            self.souphttpsrc.set_property("location", hls_url)
            self.souphttpsrc.set_property("is-live", True)
            self.souphttpsrc.set_property("timeout", 30)
            self.souphttpsrc.set_property("retries", 3)
            
            if self.pipeline_type == "hlsdemux2":
                try:
                    self.hlsdemux.set_property("connection-speed", 1000)
                except:
                    pass
        
        elif self.pipeline_type == "uridecodebin":
            self.uridecodebin.set_property("uri", hls_url)
        
        elif self.pipeline_type == "playbin":
            self.playbin.set_property("uri", hls_url)
            # Set video sink
            self.nveglglessink = Gst.ElementFactory.make("nveglglessink", "sink")
            if self.nveglglessink:
                self.playbin.set_property("video-sink", self.nveglglessink)
    
    def add_elements_to_pipeline(self):
        """Add elements to pipeline"""
        
        # Check all elements were created
        for name, element in self.elements:
            if not element:
                print(f"Failed to create {name} element")
                sys.exit(1)
        
        # Add to pipeline
        if self.pipeline_type == "playbin":
            self.pipeline.add(self.playbin)
        else:
            for name, element in self.elements:
                self.pipeline.add(element)
    
    def link_elements(self):
        """Link elements based on pipeline type"""
        
        if self.pipeline_type in ["hlsdemux2", "hlsdemux"]:
            self.link_manual_pipeline()
        elif self.pipeline_type == "uridecodebin":
            self.link_uridecodebin_pipeline()
        # playbin handles linking internally
    
    def link_manual_pipeline(self):
        """Link manual pipeline elements"""
        
        # Static links
        if not self.souphttpsrc.link(self.hlsdemux):
            print(f"Failed to link souphttpsrc to {self.pipeline_type}")
            sys.exit(1)
        
        # Other static links
        links = [
            (self.queue1, self.tsdemux),
            (self.h264parse, self.nvv4l2decoder),
            (self.nvvideoconvert, self.nveglglessink)
        ]
        
        for src, dst in links:
            if not src.link(dst):
                print(f"Failed to link {src.get_name()} to {dst.get_name()}")
                sys.exit(1)
        
        # Link with caps
        caps = Gst.Caps.from_string("video/x-raw(memory:NVMM), format=NV12")
        if not self.nvv4l2decoder.link_filtered(self.queue2, caps):
            print("Failed to link decoder to queue2")
            sys.exit(1)
        
        if not self.queue2.link(self.nvvideoconvert):
            print("Failed to link queue2 to converter")
            sys.exit(1)
    
    def link_uridecodebin_pipeline(self):
        """Link uridecodebin pipeline elements"""
        
        # Static links
        if not self.queue.link(self.nvvideoconvert):
            print("Failed to link queue to converter")
            sys.exit(1)
        
        if not self.nvvideoconvert.link(self.nveglglessink):
            print("Failed to link converter to sink")
            sys.exit(1)
    
    def connect_signals(self):
        """Connect signals based on pipeline type"""
        
        if self.pipeline_type in ["hlsdemux2", "hlsdemux"]:
            self.hlsdemux.connect("pad-added", self.on_hlsdemux_pad_added)
            self.tsdemux.connect("pad-added", self.on_tsdemux_pad_added)
        elif self.pipeline_type == "uridecodebin":
            self.uridecodebin.connect("pad-added", self.on_uridecodebin_pad_added)
        
        # Bus messages
        bus = self.pipeline.get_bus()
        bus.add_signal_watch()
        bus.connect("message", self.on_bus_message)
    
    def on_hlsdemux_pad_added(self, demux, pad):
        """Handle hlsdemux pad added"""
        sink_pad = self.queue1.get_static_pad("sink")
        if not sink_pad.is_linked():
            result = pad.link(sink_pad)
            if result == Gst.PadLinkReturn.OK:
                print(f"Successfully linked {self.pipeline_type} pad")
    
    def on_tsdemux_pad_added(self, demux, pad):
        """Handle tsdemux pad added"""
        caps = pad.get_current_caps()
        if caps:
            structure = caps.get_structure(0)
            if structure and structure.get_name().startswith("video/"):
                sink_pad = self.h264parse.get_static_pad("sink")
                if not sink_pad.is_linked():
                    result = pad.link(sink_pad)
                    if result == Gst.PadLinkReturn.OK:
                        print("Successfully linked tsdemux video pad")
    
    def on_uridecodebin_pad_added(self, decodebin, pad):
        """Handle uridecodebin pad added"""
        caps = pad.get_current_caps()
        if caps:
            structure = caps.get_structure(0)
            if structure and structure.get_name().startswith("video/"):
                sink_pad = self.queue.get_static_pad("sink")
                if not sink_pad.is_linked():
                    result = pad.link(sink_pad)
                    if result == Gst.PadLinkReturn.OK:
                        print("Successfully linked uridecodebin video pad")
    
    def on_bus_message(self, bus, message):
        """Handle bus messages"""
        t = message.type
        
        if t == Gst.MessageType.EOS:
            print("End of stream")
            self.loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}")
            print(f"Debug: {debug}")
            print(f"Source: {message.src.get_name()}")
            self.loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}")
        elif t == Gst.MessageType.STATE_CHANGED:
            if message.src == self.pipeline:
                old, new, pending = message.parse_state_changed()
                print(f"Pipeline state: {old.value_nick} -> {new.value_nick}")
    
    def run(self):
        """Run the pipeline"""
        print(f"Starting {self.pipeline_type} pipeline...")
        
        self.set_element_properties()
        
        ret = self.pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Failed to start pipeline")
            return
        
        print("Pipeline started. Press Ctrl+C to stop.")
        
        try:
            self.loop.run()
        except KeyboardInterrupt:
            print("\nStopping...")
        finally:
            self.pipeline.set_state(Gst.State.NULL)
            print("Pipeline stopped")

def main():
    try:
        pipeline = AdaptiveHLSPipeline()
        pipeline.run()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    signal.signal(signal.SIGINT, lambda s, f: sys.exit(0))
    main()
